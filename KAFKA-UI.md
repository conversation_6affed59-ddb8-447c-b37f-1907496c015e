[KAFKA-UI](http://localhost:8080)
---

1. <PERSON><PERSON> [KAFKA TOPIC](http://localhost:8080/topics): `events`.
    - Click `Create topic` button.
        ![Create Topic](./assets/usage/1.topic-creation/1-create-topic.png)
    - At `Topic Name`, input **events**.
        ![Create Event Topic](./assets/usage/1.topic-creation/2-create-event-topic.png)
        Then click `Create` button.

    - Created Topic: `events`.
        ![Create Event Topic](./assets/usage/1.topic-creation/3-created-event-topic.png)

2. Produce messages to [events topic](http://localhost:8080/topics/events#configuration).
   - Select Topic `events` and click `Produce Record` button.
        ![Produce Record](./assets/usage/2.topic-seeding-message/1.produce-message.png)

   - Add message to `Value` Text Field.
        ![Add Message to Value Text Area](./assets/usage/2.topic-seeding-message/2.prepare-message.png)
   - The message is successfully sent to events topic.
        ![Message successfully Sent](./assets/usage/2.topic-seeding-message/3.message-sent.png)
