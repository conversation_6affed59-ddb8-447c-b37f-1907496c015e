Kafka Consumer
---
Application boilerplate the consume message from kafka topic.

#### Prerequisites:
 - Docker & Docker Compose
 - Node v18.20.4
 - PNPM

#### Usage

1. Copy .env.example
    ```bash
    cp .env.example .env
    ```
2. Install the application.
   ```bash
   pnpm install
   ```
3. Run the Kafka via Docker compose.
   ```bash
   docker compose up --detach
   ```
4. Create Kafka topic: `events`.
   ```bash
   docker exec -it kafka rpk topic create events
   ```
5. Produce Message to topic: `events`.
   ```bash
   docker exec -i kafka rpk topic produce events
   ```
6. Enter a message, then press `Enter`:
   ```text
   Hello, world!.
   ```
   Example Output:
   ```text
   Produced to partition 0 at offset 6 with timestamp 1725414750121.
   ```
7. Press `Ctrl+C` to finish producing messages to the topic.
   
8. Run the application.
   ```bash
   pnpm start
   ```
9.  App will consume the message(s) right after message(s) sent.
    ![Message consumed](./assets/usage/3.app/log.png)


#### KAFKA UI
If you are a UI/WEB person, you might want to check [KAFKA UI](./KAFKA-UI.md).

#### Message Structure
Messages that are returned by the KafkaConsumer have the following structure.
```json
{
  value: new Buffer('hi'), // message contents as a Buffer
  size: 2, // size of the message, in bytes
  topic: 'events', // topic the message comes from
  offset: 1337, // offset the message was read from
  partition: 1, // partition the message was on
  key: 'someKey' // key of the message if present
}
```

#### Configuration
- `KAFKA_SASL_USERNAME`: Username for SASL authentication.
- `KAFKA_SASL_PASSWORD`: Password for SASL authentication.
- `KAFKA_SECURITY_PROTOCOL`: Security protocol for Kafka (e.g., SASL_SSL).
- `KAFKA_MECHANISM`: SASL mechanism (e.g., PLAIN).
- `KAFKA_BROKERS`: Comma-separated list of Kafka broker addresses.
- `KAFKA_CONSUMER_GROUP`: Consumer group ID.
- `KAFKA_EVENT_CB`: Boolean to enable event logging.
- `KAFKA_DEBUG_SCOPE`: Debug scope for Kafka.
- `KAFKA_AUTO_COMMIT_INTERVAL_MS`: Interval for automatic commits.
- `KAFKA_ENABLE_AUTO_COMMIT`: Boolean to enable/disable auto-commit.
- `KAFKA_AUTO_OFFSET_RESET`: Offset reset strategy (e.g., earliest or latest).

#### Event Logging
To enable detailed logging of Kafka events, make sure the `KAFKA_EVENT_CB` environment variable is set to `true`. The service will log detailed information for debugging purposes.

Detailed logging is specific to `consumer` by default. If you want to add/replace it, you can do so by configuring the `KAFKA_DEBUG_SCOPE` environment variable.

- `KAFKA_DEBUG_SCOPE` - A comma-separated list of debug contexts to enable.
   - consumer
   - cgrp
   - topic
   - fetch
