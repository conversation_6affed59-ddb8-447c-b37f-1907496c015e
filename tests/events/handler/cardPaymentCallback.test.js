/* eslint-disable no-undef */
const cardPaymentCallback = require('../../../src/events/handler/cardPaymentCallback');

// Mock all dependencies
jest.mock('@configs/logger');
jest.mock('@exception');
jest.mock('@services/xendit');
jest.mock('@services/channel');
jest.mock('@repositories/dynamodb/transactionRepository');
jest.mock('@repositories/dynamodb/channelRepository');
jest.mock('@repositories/dynamodb/eventLogsRepository');
jest.mock('@repositories/dynamodb/settlementRepository');
jest.mock('@utils/database', () => ({
  writeDDBTransaction: jest.fn()
}));
jest.mock('@utils', () => ({
  uuid: {
    generateUuidv7: jest.fn(),
    guid: jest.fn() // guid is imported directly from @utils, not from @utils.uuid
  },
  xendit: {
    basicAuth: jest.fn()
  },
  database: {
    transformJsonAttributes: jest.fn(),
    ReturnValues: {
      ALL_NEW: 'ALL_NEW',
      UPDATED_NEW: 'UPDATED_NEW',
      ALL_OLD: 'ALL_OLD',
      UPDATED_OLD: 'UPDATED_OLD'
    },
    transactionOperationWrapper: jest.fn()
  },
  date: {
    getPhDateTime: jest.fn()
  },
  kafka: {
    extractEventFromKafkaHeaders: jest.fn(),
    normalizeKafkaHeaders: jest.fn()
  },
  channel: {
    paymentProcessedPayload: jest.fn(),
    paymentInstallmentPayload: jest.fn()
  },
  kafkaSchemaRegistry: {
    encodeWithOptionalKafkaSchemaRegistryValidation: jest.fn(),
    decodeWithOptionalKafkaSchemaRegistryValidation: jest.fn()
  }
}));
// Mock Kafka producer at module level
// const mockProduceMessage = jest.fn().mockResolvedValue();
jest.mock('@configs/kafkaProducer', () => {
  const mockFn = jest.fn().mockResolvedValue();
  return jest.fn().mockImplementation(() => ({
    produceMessage: mockFn
  }));
});
jest.mock('@configs/env', () => ({
  NODE_ENV: 'local',
  LOG_LEVEL: 'debug',
  KAFKA_TOPIC_POSTING: 'test-topic',
  KAFKA_PRODUCER_SCHEME: 'test-scheme',
  KAFKA_MESSAGE_EVENT_POSTING: 'test-event',
  XENDIT_HOST: 'https://api.xendit.co',
  XENDIT_SECRET_KEY: 'test-key',
  XENDIT_GET_CHARGE_ENDPOINT: '/charges'
}));
jest.mock('@root/package.json', () => ({ name: 'test-app' }));

const logger = require('@configs/logger');
const ApplicationException = require('@exception');
const { api } = require('@services/xendit');
const { execute: channelCallback } = require('@services/channel');
const transactionRepository = require('@repositories/dynamodb/transactionRepository');
const channelRepository = require('@repositories/dynamodb/channelRepository');
const eventLogsRepository = require('@repositories/dynamodb/eventLogsRepository');
const settlementRepository = require('@repositories/dynamodb/settlementRepository');
const { writeDDBTransaction } = require('@utils/database');
const {
  uuid: { generateUuidv7, guid },
  kafkaSchemaRegistry
} = require('@utils');
const KafkaProducerService = require('@configs/kafkaProducer');

describe('cardPaymentCallback', () => {
  // let mockProducerInstance;

  beforeEach(() => {
    jest.clearAllMocks();

    generateUuidv7.mockReturnValue('test-uuid-123');
    guid.mockReturnValue('test-guid-123');
    logger.info = jest.fn();
    logger.error = jest.fn();

    // Get the mocked producer instance
    const MockedKafkaProducerService = jest.mocked(KafkaProducerService);
    mockProducerInstance = MockedKafkaProducerService.mock.results[0]?.value;

    // Mock schema registry
    kafkaSchemaRegistry.encodeWithOptionalKafkaSchemaRegistryValidation = jest
      .fn()
      .mockResolvedValue('encoded-message');
  });

  describe('successful payment callback flow', () => {
    const mockParams = {
      paymentId: 'payment-123',
      event: {
        callbackStatus: 'PAID',
        credit_card_charge_id: 'charge-123',
        transactionTimestamp: '2023-01-01T00:00:00Z'
      }
    };

    const mockTransaction = {
      paymentId: 'payment-123',
      channelId: 'channel-123',
      createDateTime: '2023-01-01T00:00:00Z',
      accountId: 'settlement-account-1',
      settlementBreakdown: [
        {
          accountId: 'settlement-account-1',
          accountType: 'G',
          mobileNumber: '**********',
          emailAddress: '<EMAIL>',
          transactionType: 'PAYMENT',
          amountValue: 100,
          brand: 'VISA'
        }
      ]
    };

    const mockChannel = {
      callbackUrl: 'https://example.com/callback'
    };

    const mockXenditResponse = {
      mid_label: 'merchant-123',
      card_issuing_bank: 'Test Bank',
      network_response: {
        card_network_descriptor: 'Test Description'
      }
    };

    const mockCallbackResponse = {
      notificationResponse: 'accepted'
    };

    beforeEach(() => {
      transactionRepository.getTransaction.mockResolvedValue(mockTransaction);
      channelRepository.getChannelById.mockResolvedValue(mockChannel);
      api.getCharge.mockResolvedValue({
        data: mockXenditResponse,
        status: 200
      });
      channelCallback.mockResolvedValue({
        data: mockCallbackResponse,
        status: 200
      });
      transactionRepository.buildUpdateParams.mockReturnValue({
        TableName: 'transactions',
        Key: { paymentId: 'payment-123' }
      });
      eventLogsRepository.buildCreateParams.mockReturnValue({
        TableName: 'event-logs',
        Item: { eventId: 'test-uuid-123' }
      });
      settlementRepository.buildCreateParams.mockReturnValue({
        TableName: 'settlements',
        Item: { transactionId: 'test-uuid-123' }
      });
      writeDDBTransaction.mockResolvedValue();
    });

    it('should process successful payment callback without installment', async () => {
      await cardPaymentCallback(mockParams);

      expect(transactionRepository.getTransaction).toHaveBeenCalledWith(
        'payment-123'
      );
      expect(channelRepository.getChannelById).toHaveBeenCalledWith(
        'channel-123'
      );
      expect(api.getCharge).toHaveBeenCalledWith('charge-123');
      expect(channelCallback).toHaveBeenCalledWith(
        {
          paymentId: 'payment-123',
          callbackStatus: 'PAID',
          description: 'Test Description',
          card_issuing_bank: 'Test Bank',
          installment: ''
        },
        'https://example.com/callback',
        false,
        3
      );
      expect(writeDDBTransaction).toHaveBeenCalled();
      expect(
        kafkaSchemaRegistry.encodeWithOptionalKafkaSchemaRegistryValidation
      ).toHaveBeenCalledWith(
        {
          eventType: 'test-event',
          paymentId: 'payment-123',
          accountDetails: {
            accountNumber: 'settlement-account-1',
            paidAt: '2023-01-01T00:00:00Z'
          }
        },
        'test-scheme'
      );
      // Note: Kafka producer is called but testing the exact call is complex due to module instantiation timing
    });

    it('should process successful payment callback with installment', async () => {
      const xenditResponseWithInstallment = {
        ...mockXenditResponse,
        installment: { term: 12, amount: 100 }
      };

      api.getCharge.mockResolvedValue({
        data: xenditResponseWithInstallment,
        status: 200
      });

      await cardPaymentCallback(mockParams);

      expect(channelCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          installment: { term: 12, amount: 100 }
        }),
        'https://example.com/callback',
        true, // isInstallment should be true
        3
      );
    });

    it('should build correct transaction update parameters', async () => {
      await cardPaymentCallback(mockParams);

      expect(transactionRepository.buildUpdateParams).toHaveBeenCalledWith(
        {
          paymentId: 'payment-123',
          createDateTime: '2023-01-01T00:00:00Z'
        },
        {
          status: 'CARD_AUTHORISED',
          merchant_id: 'merchant-123',
          paymentMethod: 'card',
          installment: '',
          card_issuing_bank: 'Test Bank',
          card_brand: ''
        }
      );
    });

    it('should build correct event log parameters', async () => {
      await cardPaymentCallback(mockParams);

      expect(eventLogsRepository.buildCreateParams).toHaveBeenCalledWith({
        paymentId: 'payment-123',
        eventId: 'test-uuid-123',
        channelId: 'channel-123',
        eventName: 'PAYMENT_CALLBACK',
        eventSource: 'test-app',
        eventStatus: 'SUCCESS_PAYMENT_CALLBACK',
        eventDetails: {
          params: mockParams
        }
      });
    });

    it('should build correct settlement parameters', async () => {
      await cardPaymentCallback(mockParams);

      expect(settlementRepository.buildCreateParams).toHaveBeenCalledWith({
        paymentId: 'payment-123',
        transactionId: 'test-uuid-123',
        createDateTime: '2023-01-01T00:00:00Z',
        accountId: 'settlement-account-1',
        accountType: 'G',
        mobileNumber: '**********',
        email: '<EMAIL>',
        transactionType: 'PAYMENT',
        amount: 100,
        brand: 'VISA',
        status: 'CARD_AUTHORISED',
        postPaymentReferenceId: 'test-guid-123'
      });
    });

    it('should send kafka message with correct payload', async () => {
      await cardPaymentCallback(mockParams);

      expect(
        kafkaSchemaRegistry.encodeWithOptionalKafkaSchemaRegistryValidation
      ).toHaveBeenCalledWith(
        {
          eventType: 'test-event',
          paymentId: 'payment-123',
          accountDetails: {
            accountNumber: 'settlement-account-1',
            paidAt: '2023-01-01T00:00:00Z'
          }
        },
        'test-scheme'
      );
      // Note: Kafka producer is called but testing the exact call is complex due to module instantiation timing
    });
  });

  describe('error scenarios', () => {
    const mockParams = {
      paymentId: 'payment-123',
      event: {
        callbackStatus: 'PAID',
        credit_card_charge_id: 'charge-123',
        transactionTimestamp: '2023-01-01T00:00:00Z'
      }
    };

    it('should handle transaction not found error', async () => {
      transactionRepository.getTransaction.mockResolvedValue(null);
      eventLogsRepository.createFailedEvent.mockResolvedValue();
      ApplicationException.mockImplementation((message, details) => {
        const error = new Error(message);
        error.details = details;
        return error;
      });

      await cardPaymentCallback(mockParams);

      expect(eventLogsRepository.createFailedEvent).toHaveBeenCalledWith({
        paymentId: 'payment-123',
        eventId: 'test-uuid-123',
        channel: { id: undefined },
        eventName: 'PAYMENT_CALLBACK',
        eventSource: 'test-app',
        error: 'TRANSACTION_NOT_FOUND'
      });
      expect(logger.error).toHaveBeenCalled();
    });

    it('should handle xendit API failure', async () => {
      const mockTransaction = {
        paymentId: 'payment-123',
        channelId: 'channel-123'
      };

      transactionRepository.getTransaction.mockResolvedValue(mockTransaction);
      api.getCharge.mockResolvedValue({
        data: { error: 'API Error' },
        status: 500
      });
      eventLogsRepository.createFailedEvent.mockResolvedValue();

      await cardPaymentCallback(mockParams);

      expect(eventLogsRepository.createFailedEvent).toHaveBeenCalledWith({
        paymentId: 'payment-123',
        eventId: 'test-uuid-123',
        channel: { id: 'channel-123' },
        eventName: 'PAYMENT_SESSION',
        eventSource: 'test-app',
        xenditResponse: { error: 'API Error' }
      });
      expect(logger.error).toHaveBeenCalled();
    });

    it('should handle channel callback failure - non-200 status', async () => {
      const mockTransaction = {
        paymentId: 'payment-123',
        channelId: 'channel-123',
        createDateTime: '2023-01-01T00:00:00Z',
        accountId: 'account-123',
        settlementBreakdown: []
      };

      const mockChannel = { callbackUrl: 'https://example.com/callback' };
      const mockXenditResponse = { mid_label: 'merchant-123' };

      transactionRepository.getTransaction.mockResolvedValue(mockTransaction);
      channelRepository.getChannelById.mockResolvedValue(mockChannel);
      api.getCharge.mockResolvedValue({
        data: mockXenditResponse,
        status: 200
      });
      channelCallback.mockResolvedValue({
        data: { notificationResponse: 'rejected' },
        status: 400
      });
      eventLogsRepository.createFailedEvent.mockResolvedValue();

      await cardPaymentCallback(mockParams);

      expect(eventLogsRepository.createFailedEvent).toHaveBeenCalledWith({
        paymentId: 'payment-123',
        eventId: 'test-uuid-123',
        channel: { id: 'channel-123' },
        eventName: 'PAYMENT_CALLBACK',
        eventSource: 'test-app',
        error: { callbackResponse: { notificationResponse: 'rejected' } }
      });
    });

    it('should handle channel callback failure - rejected notification', async () => {
      const mockTransaction = {
        paymentId: 'payment-123',
        channelId: 'channel-123',
        createDateTime: '2023-01-01T00:00:00Z',
        accountId: 'account-123',
        settlementBreakdown: []
      };

      const mockChannel = { callbackUrl: 'https://example.com/callback' };
      const mockXenditResponse = { mid_label: 'merchant-123' };

      transactionRepository.getTransaction.mockResolvedValue(mockTransaction);
      channelRepository.getChannelById.mockResolvedValue(mockChannel);
      api.getCharge.mockResolvedValue({
        data: mockXenditResponse,
        status: 200
      });
      channelCallback.mockResolvedValue({
        data: { notificationResponse: 'rejected' },
        status: 200
      });
      eventLogsRepository.createFailedEvent.mockResolvedValue();

      await cardPaymentCallback(mockParams);

      expect(eventLogsRepository.createFailedEvent).toHaveBeenCalledWith({
        paymentId: 'payment-123',
        eventId: 'test-uuid-123',
        channel: { id: 'channel-123' },
        eventName: 'PAYMENT_CALLBACK',
        eventSource: 'test-app',
        error: { callbackResponse: { notificationResponse: 'rejected' } }
      });
    });

    it('should handle general errors and log them', async () => {
      const error = new Error('Unexpected error');
      transactionRepository.getTransaction.mockRejectedValue(error);

      await cardPaymentCallback(mockParams);

      expect(logger.error).toHaveBeenCalledWith(
        error,
        'PAYMENT_CALLBACK_ERROR'
      );
    });
  });

  describe('edge cases', () => {
    const mockParams = {
      paymentId: 'payment-123',
      event: {
        callbackStatus: 'PAID',
        credit_card_charge_id: 'charge-123',
        transactionTimestamp: '2023-01-01T00:00:00Z'
      }
    };

    it('should handle xendit response with null installment', async () => {
      const mockTransaction = {
        paymentId: 'payment-123',
        channelId: 'channel-123',
        createDateTime: '2023-01-01T00:00:00Z',
        accountId: 'account-123',
        settlementBreakdown: []
      };

      const mockChannel = { callbackUrl: 'https://example.com/callback' };
      const mockXenditResponse = {
        mid_label: 'merchant-123',
        installment: null
      };

      transactionRepository.getTransaction.mockResolvedValue(mockTransaction);
      channelRepository.getChannelById.mockResolvedValue(mockChannel);
      api.getCharge.mockResolvedValue({
        data: mockXenditResponse,
        status: 200
      });
      channelCallback.mockResolvedValue({
        data: { notificationResponse: 'accepted' },
        status: 200
      });

      await cardPaymentCallback(mockParams);

      expect(channelCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          installment: ''
        }),
        'https://example.com/callback',
        false, // isInstallment should be false
        3
      );
    });

    it('should handle xendit response with missing optional fields', async () => {
      const mockTransaction = {
        paymentId: 'payment-123',
        channelId: 'channel-123',
        createDateTime: '2023-01-01T00:00:00Z',
        accountId: 'account-123',
        settlementBreakdown: []
      };

      const mockChannel = { callbackUrl: 'https://example.com/callback' };
      const mockXenditResponse = {
        mid_label: 'merchant-123'
        // Missing card_issuing_bank, network_response, installment
      };

      transactionRepository.getTransaction.mockResolvedValue(mockTransaction);
      channelRepository.getChannelById.mockResolvedValue(mockChannel);
      api.getCharge.mockResolvedValue({
        data: mockXenditResponse,
        status: 200
      });
      channelCallback.mockResolvedValue({
        data: { notificationResponse: 'accepted' },
        status: 200
      });
      transactionRepository.buildUpdateParams.mockReturnValue({});
      eventLogsRepository.buildCreateParams.mockReturnValue({});
      writeDDBTransaction.mockResolvedValue();

      await cardPaymentCallback(mockParams);

      expect(channelCallback).toHaveBeenCalledWith(
        {
          paymentId: 'payment-123',
          callbackStatus: 'PAID',
          description: '',
          card_issuing_bank: '',
          installment: ''
        },
        'https://example.com/callback',
        false,
        3
      );
    });

    it('should handle empty settlement breakdown', async () => {
      const mockTransaction = {
        paymentId: 'payment-123',
        channelId: 'channel-123',
        createDateTime: '2023-01-01T00:00:00Z',
        accountId: 'account-123',
        settlementBreakdown: [] // Empty array
      };

      const mockChannel = { callbackUrl: 'https://example.com/callback' };
      const mockXenditResponse = { mid_label: 'merchant-123' };

      transactionRepository.getTransaction.mockResolvedValue(mockTransaction);
      channelRepository.getChannelById.mockResolvedValue(mockChannel);
      api.getCharge.mockResolvedValue({
        data: mockXenditResponse,
        status: 200
      });
      channelCallback.mockResolvedValue({
        data: { notificationResponse: 'accepted' },
        status: 200
      });
      transactionRepository.buildUpdateParams.mockReturnValue({});
      eventLogsRepository.buildCreateParams.mockReturnValue({});
      writeDDBTransaction.mockResolvedValue();

      await cardPaymentCallback(mockParams);

      expect(writeDDBTransaction).toHaveBeenCalledWith([
        { params: {}, operation: 'Update' },
        { params: {}, operation: 'Put' }
        // No settlement params should be included
      ]);
    });
  });
});
