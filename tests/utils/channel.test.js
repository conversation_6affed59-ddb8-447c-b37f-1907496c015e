/* eslint-disable no-undef */
const {
  // paymentCreatedPayload,
  // paymentInstallmentPayload,
  paymentProcessedPayload
} = require('../../src/utils/channel');

// describe('paymentCreatedPayload function', () => {
//   it('should generate correct payload with mapped status', () => {
//     const input = {
//       reference_id: '12345',
//       status: 'ACTIVE',
//       payment_code: 'ABC123'
//     };
//     const result = paymentCreatedPayload(input);

//     expect(result).toEqual({
//       notification: {
//         name: 'PaymentCreated',
//         payload: {
//           paymentId: '12345',
//           accounts: [{ status: 'ECPAY_GENERATED', payment_code: 'ABC123' }]
//         }
//       }
//     });
//   });

//   it('should throw an error for invalid status', () => {
//     const input = {
//       reference_id: '12345',
//       status: 'INVALID',
//       payment_code: 'ABC123'
//     };
//     expect(() => paymentCreatedPayload(input)).toThrow(
//       'Invalid status: INVALID'
//     );
//   });
// });

describe('paymentProcessedPayload function', () => {
  it('should generate correct payload with mapped callbackStatus', () => {
    const input = {
      paymentId: '98765',
      callbackStatus: 'PAID',
      accountNumber: '**********',
      description: 'Transaction Successful'
    };
    const result = paymentProcessedPayload(input);

    expect(result).toEqual({
      notification: {
        name: 'PaymentProcessed',
        payload: {
          paymentId: '98765',
          accounts: [
            {
              status: 'CARD_AUTHORISED',
              accountNumber: '**********',
              description: 'Transaction Successful'
            }
          ]
        }
      }
    });
  });

  it('should throw an error for invalid callbackStatus', () => {
    const input = {
      paymentId: '98765',
      callbackStatus: 'UNKNOWN',
      payment_code: 'XYZ789'
    };
    expect(() => paymentProcessedPayload(input)).toThrow(
      'Invalid status: UNKNOWN'
    );
  });
});
