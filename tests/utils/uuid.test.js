/* eslint-disable no-undef */
const { generateUuidv4, generateUuidv7 } = require('../../src/utils/uuid');
const { validate: validateUuid, version: getUuidVersion } = require('uuid');

describe('UUID Generator', () => {
  test('generateUuidv4 should return a valid UUID v4', () => {
    const uuid = generateUuidv4();
    expect(validateUuid(uuid)).toBe(true);
    expect(getUuidVersion(uuid)).toBe(4);
  });

  test('generateUuidv7 should return a valid UUID v7', () => {
    const uuid = generateUuidv7();
    expect(validateUuid(uuid)).toBe(true);
    expect(getUuidVersion(uuid)).toBe(7);
  });
});
