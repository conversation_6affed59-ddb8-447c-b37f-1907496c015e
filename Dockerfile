ARG ARTIFACTORY_URL
# =========================================================================
# ============================ BUILDER IMAGE ==============================
# =========================================================================
FROM $ARTIFACTORY_URL/isg-gpayo-docker-local/node:v22.14.0 AS builder

RUN apk --no-cache add \
    bash \
    g++ \
    make \
    python3 \
    zlib-dev \
    libc-dev \
    bsd-compat-headers \
    py-setuptools \
    openssl-dev \
    musl-dev

WORKDIR /home/<USER>/app

# COPY APPLICATION FILES
COPY . .

RUN npm install -g husky@9.1.5  \
    && npm ci --omit=dev --verbose

# =========================================================================
# ============================ RUNTIME IMAGE ==============================
# =========================================================================
FROM $ARTIFACTORY_URL/isg-gpayo-docker-local/node:v22.14.0

# COPY ARTIFACTS TO THE BUILDER STAGE
COPY --chown=node:node --from=builder /home/<USER>/app /home/<USER>/app

WORKDIR /home/<USER>/app

USER node

ENTRYPOINT ["npm", "run", "start"]