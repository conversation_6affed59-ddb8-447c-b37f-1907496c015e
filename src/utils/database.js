const ddbClient = require('@configs/dynamodb');
const { TransactWriteCommand } = require('@aws-sdk/lib-dynamodb');
const { DDB_TTL_DAYS } = require('@configs/env');

const transformJsonAttributes = (attributes, returnValues = '') => {
  const attributesName = Object.keys(attributes);

  const updateExpression = attributesName
    .map((attr) => `#${attr} = :${attr}`)
    .join(', ');

  const expressionAttributeNames = Object.fromEntries(
    Object.keys(attributes).map((attr) => [`#${attr}`, attr])
  );

  const expressionAttributeValues = Object.fromEntries(
    Object.keys(attributes).map((attr) => [`:${attr}`, attributes[attr]])
  );

  const output = {
    UpdateExpression: `SET ${updateExpression}`,
    ExpressionAttributeNames: expressionAttributeNames,
    ExpressionAttributeValues: expressionAttributeValues,
    ReturnValues: ReturnValues.UPDATED_NEW // Default
  };

  switch (returnValues) {
    case ReturnValues.ALL_NEW:
      output.ReturnValues = ReturnValues.ALL_NEW;
      break;
    case ReturnValues.ALL_OLD:
      output.ReturnValues = ReturnValues.ALL_OLD;
      break;
    case ReturnValues.UPDATED_OLD:
      output.ReturnValues = ReturnValues.UPDATED_OLD;
      break;
    default:
      break;
  }
  return output;
};

const ReturnValues = {
  ALL_NEW: 'ALL_NEW',
  UPDATED_NEW: 'UPDATED_NEW',
  ALL_OLD: 'ALL_OLD',
  UPDATED_OLD: 'UPDATED_OLD'
};

const transactionOperationWrapper = (item, action = 'Put') => {
  return { [action]: item };
};

async function writeDDBTransaction(operations = []) {
  if (!Array.isArray(operations) || operations.length === 0) {
    throw new Error('Invalid operations array');
  }

  const transactItems = operations.map((op) => {
    return transactionOperationWrapper(op.params, op.operation || 'Put');
  });

  return ddbClient.send(
    new TransactWriteCommand({
      TransactItems: transactItems
    })
  );
}

const getTTL = () => {
  const days = parseInt(DDB_TTL_DAYS) || 365;
  const secondsInDay = 24 * 60 * 60;
  const ttlInSeconds = days * secondsInDay;

  return Math.floor(Date.now() / 1000) + ttlInSeconds;
};

module.exports = {
  transformJsonAttributes,
  ReturnValues,
  transactionOperationWrapper,
  writeDDBTransaction,
  getTTL
};
