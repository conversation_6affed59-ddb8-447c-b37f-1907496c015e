const { SchemaRegistry } = require('@kafkajs/confluent-schema-registry');
const {
  KAFKA_SCHEMA_REGISTRY_TOGGLE,
  KAFKA_SCHEMA_REGISTRY_HOST,
  KAFKA_SCHEMA_REGISTRY_USERNAME,
  KAFKA_SCHEMA_REGISTRY_PASSWORD
} = require('@configs/env');
const logger = require('@configs/logger');

/**
 * Create an instance of the schema registry
 */
const schemaRegistry = new SchemaRegistry({
  host: KAFKA_SCHEMA_REGISTRY_HOST,
  auth: {
    username: KAFKA_SCHEMA_REGISTRY_USERNAME,
    password: KAFKA_SCHEMA_REGISTRY_PASSWORD
  }
});

const parseJsonOrReturn = (message) => {
  return typeof message === 'object' ? JSON.parse(message) : message;
};

const decodeWithOptionalKafkaSchemaRegistryValidation = async (
  kafkaMessage
) => {
  if (KAFKA_SCHEMA_REGISTRY_TOGGLE) {
    return schemaRegistry.decode(kafkaMessage);
  }
  return parseJsonOrReturn(kafkaMessage);
};

const encodeWithOptionalKafkaSchemaRegistryValidation = async (
  kafkaMessage,
  schemaId
) => {
  try {
    if (KAFKA_SCHEMA_REGISTRY_TOGGLE) {
      const encodedMessage = await schemaRegistry.encode(
        schemaId,
        kafkaMessage
      );
      return encodedMessage;
    }
    return Buffer.from(JSON.stringify(kafkaMessage));
  } catch (error) {
    logger.error(error, 'KAFKA_SCHEMA_ERROR');
    throw error;
  }
};

module.exports = {
  decodeWithOptionalKafkaSchemaRegistryValidation,
  encodeWithOptionalKafkaSchemaRegistryValidation
};
