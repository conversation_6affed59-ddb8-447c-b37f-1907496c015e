const normalizeKafkaHeaders = (headers) => {
  if (!headers) {
    throw new Error('[Bad Request]: kafka message headers are missing.');
  }

  return headers.reduce((accumulator, header) => {
    // Get the first key
    const [headerKey] = Object.keys(header);
    // Convert to string or set to null
    accumulator[headerKey] = header[headerKey]?.toString() || null;
    return accumulator;
  }, {});
};

const extractEventFromKafkaHeaders = (
  normalizedKafkaHeaders,
  eventHandlers
) => {
  if (!normalizedKafkaHeaders.eventType) {
    throw new Error(
      '[Bad Request]: Kafka message header: eventType is not found.'
    );
  }
  // toString due to value is on Buffer.
  const eventName = normalizedKafkaHeaders.eventType.toString();
  if (!Object.hasOwn(eventHandlers, eventName)) {
    throw new Error(`[Bad Request]: Event: ${eventName} is not supported.`);
  }
  return eventName;
};

module.exports = {
  extractEventFromKafkaHeaders,
  normalizeKafkaHeaders
};
