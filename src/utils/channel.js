const XENDIT_STATUS_MAPPING = {
  PAID: 'CARD_AUTHORISED',
  EXPIRED: 'CARD_EXPIRED'
};

const getStatus = (status) => {
  const s = XENDIT_STATUS_MAPPING[status];
  if (s) {
    return s;
  }
  throw new Error(`Invalid status: ${status}`);
};

const paymentProcessedPayload = ({
  paymentId,
  callbackStatus,
  description,
}) => {
  const payloadBody = {
    notification: {
      name: 'PaymentProcessed',
      payload: {
        paymentId,
        status: getStatus(callbackStatus),
        description
      }
    }
  };
  return payloadBody;
};

const paymentInstallmentPayload = ({
  paymentId,
  callbackStatus,
  description,
  card_issuing_bank,
  installment,
}) => {
  const payloadBody = {
    notification: {
      name: 'PaymentProcessed',
      payload: {
        paymentId,
        installmentDetails: {
          bank: card_issuing_bank,
          term: installment.count,
          interval: installment.interval
        },
        status: getStatus(callbackStatus),
        description
      }
    }
  };
  return payloadBody;
};

module.exports = {
  paymentProcessedPayload,
  paymentInstallmentPayload
};
