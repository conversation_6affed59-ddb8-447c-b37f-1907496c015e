const fs = require('fs');

const stringType = {
  type: 'string'
};

const numberTypeDefault = (defaultValue) => {
  return { type: 'integer', default: defaultValue };
};

const booleanTypeDefault = (defaultValue = false) => {
  return {
    type: 'boolean',
    default: defaultValue
  };
};

const stringTypeDefault = (defaultValue) => {
  return { ...stringType, default: defaultValue };
};

const enums = (values) => {
  return { enum: values.split(',') };
};

const getFromFile = (path) => fs.readFileSync(path, 'utf8');

module.exports = {
  stringType,
  booleanTypeDefault,
  stringTypeDefault,
  getFromFile,
  numberTypeDefault,
  enums
};
