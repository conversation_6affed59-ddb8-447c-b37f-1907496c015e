const {
  stringTypeDefault,
  stringType,
  booleanTypeDefault,
  numberTypeDefault,
  enums
} = require('./util');

const kafkaConfigs = {
  KAFKA_TOPICS: stringType,
  KAFKA_BROKERS: stringType,
  KAFKA_SASL_USERNAME: stringType,
  KAFKA_SASL_PASSWORD: stringType,
  KAFKA_SECURITY_PROTOCOL: stringType,
  KAFKA_MECHANISM: stringType,
  KAFKA_ENDPOINT_IDENTIFICATION_ALGORITHM: stringTypeDefault('https'),
  KAFKA_CONSUMER_GROUP: stringType,
  KAFKA_DEBUG_SCOPE: stringTypeDefault('consumer'),
  KAFKA_EVENT_LOG: booleanTypeDefault(true),
  KAFKA_EVENT_CB: booleanTypeDefault(true),
  KAFKA_AUTO_COMMIT_INTERVAL_MS: numberTypeDefault(5000),
  KAFKA_AUTO_OFFSET_RESET: enums(
    'smallest,earliest,beginning,largest,latest,end,error'
  ),
  KAFKA_SCHEMA_REGISTRY_HOST: stringType,
  KAFKA_SCHEMA_REGISTRY_USERNAME: stringType,
  KAFKA_SCHEMA_REGISTRY_PASSWORD: stringType,
  KAFKA_SCHEMA_REGISTRY_TOGGLE: booleanTypeDefault(false),
  KAFKA_SCHEMA_REGISTRY_SUFFIX: stringTypeDefault('value'),
  KAFKA_TOPIC_POSTING: stringType,
  KAFKA_PRODUCER_SCHEME: stringType,
  KAFKA_PRODUCE_BROKERS: stringType,
  KAFKA_MESSAGE_EVENT_POSTING: stringType,
  KAFKA_PRODUCER_SASL_USERNAME: stringType,
  KAFKA_PRODUCER_SASL_PASSWORD: stringType,
  KAFKA_PRODUCER_SECURITY_PROTOCOL: stringType,
  KAFKA_PRODUCER_MECHANISM: stringType,
  KAFKA_PRODUCER_QUEUE_BUFFERING_MAX_KBYTES: stringType,
  KAFKA_PRODUCER_LINGER_MS: stringType,
  KAFKA_PRODUCER_MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION: stringType,
  KAFKA_PRODUCER_RETRIES: stringType,
  KAFKA_PRODUCER_BATCH_NUM_MESSAGES: stringType,
  KAFKA_PRODUCER_ACKS: stringType
};

const kafkaConsumerConfigurations = {
  KAFKA_CONSUMER_ENABLE_AUTO_COMMIT: booleanTypeDefault(false),

  KAFKA_CONSUMER_READ_NUMBER_OF_MESSAGES_PER_INTERVAL: numberTypeDefault(100),
  KAFKA_CONSUMER_READ_INTERVAL_MS: numberTypeDefault(1000),

  // LOCAL QUEUE
  KAFKA_CONSUMER_LOCAL_QUEUE_MAX_MESSAGES_KBYTES: numberTypeDefault(512),
  KAFKA_CONSUMER_LOCAL_QUEUE_MIN_MESSAGES: numberTypeDefault(100),

  // REQUEST THROTTLING
  KAFKA_CONSUMER_REQUEST_WAIT_MAX_MS: numberTypeDefault(5000),
  KAFKA_CONSUMER_REQUEST_ERROR_BACKOFF_MS: numberTypeDefault(500),

  // BATCH SIZE PER POLL
  KAFKA_CONSUMER_BATCH_MAX_MESSAGE_BYTES: numberTypeDefault(4096)
};

const xenditConfig = {
  XENDIT_HOST: stringType,
  XENDIT_SECRET_KEY: stringType,
  XENDIT_CREATE_PAYMENT_CODE_ENDPOINT: stringType,
  XENDIT_GET_CHARGE_ENDPOINT: stringType
};

const awsConfig = {
  AWS_REGION: stringType,
  AWS_DYNAMODB_ENDPOINT: stringType
};

const dynamoDBConfig = {
  DB_TABLE_TRANSACTION: stringType,
  DB_TABLE_CHANNEL: stringType,
  DB_TABLE_EVENT_LOGS: stringType,
  DB_TABLE_SETTLEMENT: stringType,
  DDB_TTL_DAYS: numberTypeDefault(365)
};

const schema = {
  type: 'object',
  required: [
    'KAFKA_TOPICS',
    'KAFKA_BROKERS',
    'KAFKA_SASL_USERNAME',
    'KAFKA_SASL_PASSWORD',
    'KAFKA_SECURITY_PROTOCOL',
    'KAFKA_MECHANISM',
    'DB_TABLE_TRANSACTION',
    'DB_TABLE_CHANNEL',
    'DB_TABLE_EVENT_LOGS',
    'DB_TABLE_SETTLEMENT',
    'XENDIT_HOST',
    'XENDIT_SECRET_KEY',
    'XENDIT_CREATE_PAYMENT_CODE_ENDPOINT',
    'XENDIT_GET_CHARGE_ENDPOINT',
    'CXS_CALLBACK_API_KEY',
    'KAFKA_TOPIC_POSTING',
    'KAFKA_PRODUCER_SCHEME',
    'KAFKA_PRODUCE_BROKERS',
    'KAFKA_MESSAGE_EVENT_POSTING',
    'KAFKA_PRODUCER_SASL_USERNAME',
    'KAFKA_PRODUCER_SASL_PASSWORD',
    'KAFKA_PRODUCER_SECURITY_PROTOCOL',
    'KAFKA_PRODUCER_MECHANISM',
    'KAFKA_PRODUCER_QUEUE_BUFFERING_MAX_KBYTES',
    'KAFKA_PRODUCER_LINGER_MS',
    'KAFKA_PRODUCER_MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION',
    'KAFKA_PRODUCER_RETRIES',
    'KAFKA_PRODUCER_BATCH_NUM_MESSAGES',
    'KAFKA_PRODUCER_ACKS'
  ],
  properties: {
    NODE_ENV: stringTypeDefault('local'),
    LOG_LEVEL: stringTypeDefault('debug'),
    ...kafkaConfigs,
    ...awsConfig,
    ...dynamoDBConfig,
    ...kafkaConsumerConfigurations,
    ...xenditConfig,
    CXS_CALLBACK_API_KEY: stringType
  }
};

module.exports = schema;
