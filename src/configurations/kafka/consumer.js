const Kafka = require('node-rdkafka');
const logger = require('@configs/logger');
const env = require('@configs/env');
const crypto = require('crypto');
// const utilities = require('@utilities');

class KafkaConsumerService {
  #instance;
  // #tracing;

  constructor(topics) {
    this.topics = topics.split(',');
    this.#instance = null;
    // this.#tracing = new utilities.Tracing();
    this.#instanceCreation();
    this.#establishConnection();
    this.#printEventLogs();
    this.#shutdownEvents();
  }

  #getGroupId() {
    return env.NODE_ENV === 'local'
      ? `${env.NODE_ENV}:${crypto.randomBytes(8).toString('hex')}`
      : env.KAFKA_CONSUMER_GROUP;
  }

  #instanceCreation() {
    logger.info('KAFKA CONSUMER: Initializing Kafka consumer.');

    // Only include auth configurations if not in local environment
    const authConfigurations =
      process.env.NODE_ENV !== 'local'
        ? {
            'sasl.username': env.KAFKA_SASL_USERNAME,
            'sasl.password': env.KAFKA_SASL_PASSWORD,
            'security.protocol': env.KAFKA_SECURITY_PROTOCOL,
            'sasl.mechanisms': env.KAFKA_MECHANISM,
            'ssl.endpoint.identification.algorithm':
              env.KAFKA_ENDPOINT_IDENTIFICATION_ALGORITHM
          }
        : {};

    const optionalConfigurations = {
      ...authConfigurations,
      'enable.auto.commit': env.KAFKA_CONSUMER_ENABLE_AUTO_COMMIT,

      // REQUEST THROTTLING CONTROL
      'fetch.wait.max.ms': env.KAFKA_CONSUMER_REQUEST_WAIT_MAX_MS,
      'fetch.error.backoff.ms': env.KAFKA_CONSUMER_REQUEST_ERROR_BACKOFF_MS,

      // LOCAL QUEUE SIZE CONTROL
      'queued.max.messages.kbytes':
        env.KAFKA_CONSUMER_LOCAL_QUEUE_MAX_MESSAGES_KBYTES,
      'queued.min.messages': env.KAFKA_CONSUMER_LOCAL_QUEUE_MIN_MESSAGES,

      // FETCH BATCH SIZE CONTROL
      'fetch.message.max.bytes': env.KAFKA_CONSUMER_BATCH_MAX_MESSAGE_BYTES
    };

    logger.info(optionalConfigurations);
    logger.info(`schemaRegistry user: $${env.KAFKA_SCHEMA_REGISTRY_USERNAME}`);
    logger.info(`schemaRegistry pass: $${env.KAFKA_SCHEMA_REGISTRY_PASSWORD}`);

    const getKafkaConfig = () => ({
      'group.id': this.#getGroupId(),
      'bootstrap.servers': env.KAFKA_BROKERS,
      event_cb: env.KAFKA_EVENT_CB,
      debug: env.KAFKA_DEBUG_SCOPE,
      ...Object.fromEntries(
        Object.entries(optionalConfigurations).filter(
          ([_, value]) => value != null
        )
      )
    });

    this.#instance = new Kafka.KafkaConsumer(getKafkaConfig(), {
      'auto.offset.reset': env.KAFKA_AUTO_OFFSET_RESET
    });
  }

  #establishConnection() {
    logger.info(
      `KAFKA CONSUMER: Establishing connection to Kafka broker(s): ${env.KAFKA_BROKERS}.`
    );
    this.#instance.connect();

    this.#instance.once('ready', () => {
      logger.info(
        `KAFKA CONSUMER: Is now ready, proceeding to topic: ${this.topics} subscription.`
      );

      this.#instance.subscribe(this.topics);

      setInterval(() => {
        this.#instance.consume(
          env.KAFKA_CONSUMER_READ_NUMBER_OF_MESSAGES_PER_INTERVAL
        );
      }, env.KAFKA_CONSUMER_READ_INTERVAL_MS);

      logger.info(
        `KAFKA CONSUMER: Starting to consume messages from topic: ${this.topics}.`
      );
    });

    this.#instance.on('subscribed', (topics) => {
      logger.info(
        `KAFKA CONSUMER: Successfully subscribed to topic(s): ${topics}.`
      );
    });
  }

  setMessageHandler(cb) {
    // this.#instance.on('data', (msg) => {
    //   const { topic, offset, partition } = msg;
    //   this.#tracing.start(topic, msg, cb, {
    //     'kafka.topic': topic,
    //     'kafka.offset': offset,
    //     'kafka.partition': partition
    //   });
    //   this.#instance.commitMessage(msg);
    // });
    this.#instance.on('data', (msg) => {
      try {
        cb(msg);
      } finally {
        this.#instance.commitMessage(msg);
      }
    });
  }

  setErrorHandler(cb) {
    this.#instance.on('event.error', cb);

    /**
     * DOMAIN_EVENTS_ERROR
     */
    this.#instance.on('rebalance', (error, assignments) =>
      logger.error({ error, assignments }, 'DOMAIN_EVENTS_ERROR')
    );

    this.#instance.on('rebalance.error', (error) =>
      logger.error(error, 'DOMAIN_EVENTS_ERROR')
    );

    /**
     * Event is emitted in case of the ff:
     * - UNKNOWN_TOPIC_OR_PART
     * - TOPIC_AUTHORIZATION_FAILED
     */
    this.#instance.on('warning', (error) =>
      logger.error(error, 'UNKNOWN_TOPIC_OR_PART/TOPIC_AUTHORIZATION_FAILED')
    );

    this.#instance.on('connection.failure', (error) =>
      logger.error(error, 'CONNECTION FAILURE')
    );
  }

  #printEventLogs() {
    if (env.KAFKA_EVENT_CB) {
      this.#instance.on('event.log', ({ message }) => logger.debug(message));
    }
  }

  #shutdownEvents() {
    const disconnectConnection = () => {
      if (this.#instance) {
        logger.info('KAFKA CONSUMER: Disconnecting Kafka consumer.');
        this.#instance.disconnect();
      }
    };

    process.once('SIGINT', () => disconnectConnection());
    process.once('SIGTERM', () => disconnectConnection());

    this.#instance.on('disconnected', () =>
      logger.info('KAFKA CONSUMER: Successfully disconnected.')
    );
  }
}

module.exports = KafkaConsumerService;
