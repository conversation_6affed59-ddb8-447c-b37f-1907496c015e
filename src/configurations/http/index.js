const axios = require('axios');
const { default: axiosRetry } = require('axios-retry');
const axiosLogger = require('axios-logger');
const logger = require('@configs/logger');
const https = require('https');
const env = require('./schema');

class Http {
  constructor(baseURL, options = {}) {
    if (!baseURL && process.env.NODE_ENV !== 'local') {
      throw new Error('baseURL is required for non-local environments');
    }

    this.client = axios.create({
      baseURL: baseURL || '', // Default to empty string if no baseURL provided
      timeout: env.HTTP_TIMEOUT,
      httpsAgent: new https.Agent({
        rejectUnauthorized: options.rejectUnauthorized
      })
    });

    this.#httpLoggerConfiguration(options.requestResponseLogOptions);
    this.#requestRetriesConfiguration(options.retriesOptions);
  }

  /**
   * Configure HTTP request/response logging interceptors.
   * @param {Object} options - Configuration options for logging.
   */
  #httpLoggerConfiguration(options = {}) {
    const LOGGER_CONFIG = {
      prefixText: options.prefixText || this.constructor.name,
      params: options.params || env.HTTP_LOG_PARAMS,
      data: options.data || env.HTTP_LOG_DATA,
      headers: options.headers || env.HTTP_LOG_HEADERS,
      status: options.status || env.HTTP_LOG_RESPONSE_STATUS,
      logger: logger.info.bind(logger)
    };

    this.client.interceptors.request.use((req) =>
      axiosLogger.requestLogger(req, LOGGER_CONFIG)
    );

    this.client.interceptors.response.use(
      (res) => axiosLogger.responseLogger(res, LOGGER_CONFIG),
      (err) => {
        if (err.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          logger.error(
            err.response?.data || err.message,
            `HTTP:${err.status} ${err.code} ${err.config.baseURL}/${err.config.url}`
          );
        } else if (err.request) {
          logger.error('The request was made but no response was received.');
        } else {
          logger.error(err.message, 'ERROR');
        }
        return axiosLogger.errorLogger(err, {
          ...LOGGER_CONFIG,
          logger: logger.error.bind(logger)
        });
      }
    );
  }

  /**
   * Configure HTTP request retries with exponential backoff.
   * @param {Object} optionsions - Configuration options for retries.
   */
  #requestRetriesConfiguration(options = {}) {
    axiosRetry(this.client, {
      retries: options.retries || env.HTTP_MAX_RETRIES,
      retryDelay: (retryCount) => {
        logger.debug(`Retry attempt: ${retryCount}`);
        return retryCount * 1000;
      },
      retryCondition:
        options.retryCondition || axiosRetry.isNetworkOrIdempotentRequestError
    });
  }
}

module.exports = Http;
