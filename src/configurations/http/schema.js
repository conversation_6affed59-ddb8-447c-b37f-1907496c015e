const envSchema = require('env-schema');

const schema = {
  type: 'object',
  properties: {
    HTTP_MAX_RETRIES: {
      type: 'integer',
      default: 3
    },
    HTTP_TIMEOUT: {
      type: 'integer',
      default: 30000
    },
    HTTP_LOG_STATUS: {
      type: 'boolean',
      default: true
    },
    HTTP_LOG_HEADERS: {
      type: 'boolean',
      default: true
    },
    HTTP_LOG_PARAMS: {
      type: 'boolean',
      default: true
    },
    HTTP_LOG_DATA: {
      type: 'boolean',
      default: true
    },
    HTTP_LOG_RESPONSE_STATUS: {
      type: 'boolean',
      default: true
    }
  }
};

module.exports = envSchema({
  dotenv: true,
  schema
});
