// Kafka Library
const Kafka = require('node-rdkafka');

// const { trace } = require('@utils');

// Envs
const {
  KAFKA_PRODUCE_BROKERS,
  KAFKA_PRODUCER_SASL_USERNAME,
  KAFKA_PRODUCER_SASL_PASSWORD,
  KAFKA_PRODUCER_SECURITY_PROTOCOL,
  KAFKA_PRODUCER_MECHANISM,
  KAFKA_PRODUCER_QUEUE_BUFFERING_MAX_KBYTES,
  KAFKA_PRODUCER_LINGER_MS,
  KAFKA_PRODUCER_MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION,
  KAFKA_PRODUCER_RETRIES,
  KAFKA_PRODUCER_BATCH_NUM_MESSAGES,
  KAFKA_PRODUCER_ACKS
} = require('@configs/env');

// Logging
const logger = require('@configs/logger');

// Kafka Producer class
class KafkaProducerService {
  constructor(topic) {
    this.topic = topic;
    this.producer = null;
    this.createProducer();
    this.shutdownEvents();
  }

  // Method to initialize Kafka Producer
  createProducer() {
    logger.info(`KAFKA_PRODUCER_${this.topic}: Initializing Kafka producer.`);

    const optionalConfigurations = {
      'sasl.username': KAFKA_PRODUCER_SASL_USERNAME,
      'sasl.password': KAFKA_PRODUCER_SASL_PASSWORD,
      'security.protocol': KAFKA_PRODUCER_SECURITY_PROTOCOL,
      'sasl.mechanisms': KAFKA_PRODUCER_MECHANISM,
      'queue.buffering.max.kbytes': KAFKA_PRODUCER_QUEUE_BUFFERING_MAX_KBYTES,
      'linger.ms': KAFKA_PRODUCER_LINGER_MS,
      'max.in.flight.requests.per.connection':
        KAFKA_PRODUCER_MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION,
      retries: KAFKA_PRODUCER_RETRIES,
      'batch.num.messages': KAFKA_PRODUCER_BATCH_NUM_MESSAGES
    };

    // Create the Kafka producer
    this.producer = new Kafka.Producer(
      {
        'bootstrap.servers': KAFKA_PRODUCE_BROKERS,
        ...Object.fromEntries(
          Object.entries(optionalConfigurations).filter(
            ([_, value]) => value != null
          )
        )
      },
      {
        acks: KAFKA_PRODUCER_ACKS
      }
    );

    // Connect the producer to Kafka brokers
    this.producer.connect();

    this.producer.once('ready', () => {
      logger.info(`KAFKA_PRODUCER_${this.topic}: Instance is now ready.`);
    });
  }

  // method to produce message to Kafka topic
  async produceMessage(data, eventName, consumerTopic) {
    if (!this.producer) {
      throw new Error('Failed to connect to producer');
    }

    try {
      // const traceparent = trace.getTraceparent();
      // headers to be passed with event
      const headers = [
        {
          eventName: eventName
        }
        // {
        //   traceparent
        // }
      ];

      const date = new Date().toLocaleString('en-US', {
        timeZone: 'Asia/Manila'
      });

      const timestamp = new Date(date).getTime();
      logger.debug(`Kafka Topic: ${this.topic}`);
      // Produce the message to the specified Kafka topic
      await this.producer.produce(
        this.topic, // Topic
        null, // Partition (null for automatic)
        data, // Message data as buffer
        null, // Key (null if not needed)
        timestamp, // Optional timestamp
        null, // Opaque (null)
        headers // Headers with key-value pairs
      );

      logger.info(
        {
          eventName,
          eventSource: consumerTopic,
          payload: data.toString()
        },
        `SUCCESSFULLY_PRODUCED_MESSAGE: ${this.topic}`
      );
    } catch (err) {
      logger.error(`Error producing message: ${err.message}`);
      throw err;
    }
  }

  // Shutdown events to handle producer disconnection
  shutdownEvents() {
    const disconnect = () => {
      if (this.producer) {
        logger.info(
          `KAFKA_PRODUCER_${this.topic}: Disconnecting Kafka producer.`
        );
        this.producer.disconnect();
      }
    };

    // Handle termination signals for a graceful shutdown
    process.once('SIGINT', () => disconnect());
    process.once('SIGTERM', () => disconnect());

    if (this.producer) {
      this.producer.on('disconnected', () =>
        logger.info(`KAFKA_PRODUCER_${this.topic}: Successfully disconnected.`)
      );
    }
  }
}

module.exports = KafkaProducerService;
