const env = require('./schema');

const clientConfig = {
  region: env.DDB_AWS_REGION,
  endpoint: env.DDB_ENDPOINT,
  maxAttempts: env.DDB_MAX_ATTEMPTS
};

const unmarshallOptions = {
  wrapNumbers: false
};

// Options for marshalling and unmarshalling
const marshallOptions = {
  convertEmptyValues: false,
  removeUndefinedValues: false,
  convertClassInstanceToMap: false
};

const translationConfig = { marshallOptions, unmarshallOptions };

module.exports = {
  clientConfig,
  translationConfig
};
