const { DB_TABLE_CHANNEL } = require('@configs/env');
const BaseRepository = require('./baseRepository');

class ChannelRepository extends BaseRepository {
  static instance;
  constructor() {
    super(DB_TABLE_CHANNEL);
    ChannelRepository.instance = this;
  }

  static getInstance() {
    if (!ChannelRepository.instance) {
      ChannelRepository.instance = new ChannelRepository();
    }
    return ChannelRepository.instance;
  }

  async getChannelById(id) {
    const params = {
      FilterExpression: 'id = :id',
      ExpressionAttributeValues: {
        ':id': id
      }
    };
    const { Items: [channel] = [] } = await this.scan(params);
    return channel ?? null;
  }
}

module.exports = ChannelRepository.getInstance();
