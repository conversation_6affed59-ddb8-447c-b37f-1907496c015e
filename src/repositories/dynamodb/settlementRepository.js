const { DB_TABLE_SETTLEMENT } = require('@configs/env');
const BaseRepository = require('./baseRepository');

class SettlementRepository extends BaseRepository {
  static instance;
  constructor() {
    super(DB_TABLE_SETTLEMENT);
    SettlementRepository.instance = this;
  }

  static getInstance() {
    if (!SettlementRepository.instance) {
      SettlementRepository.instance = new SettlementRepository();
    }
    return SettlementRepository.instance;
  }
}

module.exports = SettlementRepository.getInstance();
