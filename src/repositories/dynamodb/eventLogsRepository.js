const { DB_TABLE_EVENT_LOGS } = require('@configs/env');
const BaseRepository = require('./baseRepository');
const ApplicationException = require('@exception');
const {
  date: { getPhDateTime }
} = require('@utils');

class EventLogsRepository extends BaseRepository {
  static instance;
  constructor() {
    super(DB_TABLE_EVENT_LOGS);
    EventLogsRepository.instance = this;
  }

  static getInstance() {
    if (!EventLogsRepository.instance) {
      EventLogsRepository.instance = new EventLogsRepository();
    }
    return EventLogsRepository.instance;
  }

  async createEvent(event) {
    const item = {
      ...event,
      createDateTime: getPhDateTime()
    };

    const { $metadata: res } = await this.save({
      Item: item
    });

    if (res.httpStatusCode !== 200) {
      throw new ApplicationException('EVENT_CREATION_ERROR', {
        error: res,
        transientState: item
      });
    }
  }

  async createFailedEvent({
    paymentId,
    eventId,
    channel,
    eventName,
    eventSource,
    error = {}
  }) {
    await this.createEvent({
      paymentId,
      eventId,
      channelId: channel?.id,
      eventName,
      eventSource,
      eventStatus: `FAILED_${eventName}`,
      errorMessage: error
    });
  }
}

module.exports = EventLogsRepository.getInstance();
