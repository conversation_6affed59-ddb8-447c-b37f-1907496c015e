const ApplicationException = require('@exception');
const { DB_TABLE_TRANSACTION } = require('@configs/env');
const BaseRepository = require('./baseRepository');
const {
  database: {
    transformJsonAttributes,
    ReturnValues,
    transactionOperationWrapper
  },
  date: { getPhDateTime }
} = require('@utils');

class TransactionRepository extends BaseRepository {
  static instance;
  constructor() {
    super(DB_TABLE_TRANSACTION);
    TransactionRepository.instance = this;
  }

  static getInstance() {
    if (!TransactionRepository.instance) {
      TransactionRepository.instance = new TransactionRepository();
    }
    return TransactionRepository.instance;
  }

  async updateTransaction(updateParams, eventParams) {
    const attributes = {
      ...updateParams.attributes,
      updateDateTime: getPhDateTime()
    };

    const updateTransactionParams = {
      TableName: updateParams.TableName,
      Key: updateParams.keys,
      ...transformJsonAttributes(attributes, ReturnValues.ALL_NEW)
    };

    const transactionQueries = [
      transactionOperationWrapper(updateTransactionParams, 'Update'),
      transactionOperationWrapper(eventParams)
    ];

    const {
      $metadata: { httpStatusCode }
    } = await this.transactional(transactionQueries);

    if (httpStatusCode !== 200) {
      throw new ApplicationException('TRANSACTION_UPDATE_ERROR', {
        queries: transactionQueries
      });
    }
  }

  async getTransaction(paymentId) {
    const params = {
      KeyConditionExpression: '#pk = :paymentId',
      ExpressionAttributeNames: {
        '#pk': 'paymentId'
      },
      ExpressionAttributeValues: {
        ':paymentId': paymentId
      }
    };

    const { Items: [transaction] = [] } = await this.query(params);

    return transaction ?? null;
  }
}

module.exports = TransactionRepository.getInstance();
