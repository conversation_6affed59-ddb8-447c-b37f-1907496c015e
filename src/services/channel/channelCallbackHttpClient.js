const logger = require('@configs/logger');
const Http = require('../../configurations/http');
const { CXS_CALLBACK_API_KEY } = require('@configs/env');

class channelCallbackHttpClient extends Http {
  constructor() {
    super({});
    this.client.interceptors.request.use(
      this.#setAuthorizationHeader.bind(this),
      { synchronous: true }
    );
  }

  static getInstance() {
    if (!channelCallbackHttpClient.instance) {
      channelCallbackHttpClient.instance = new channelCallbackHttpClient();
    }
    return channelCallbackHttpClient.instance;
  }

  async #setAuthorizationHeader(config) {
    try {
      config.headers['x-api-key'] = CXS_CALLBACK_API_KEY;
      config.headers['Content-Type'] = 'application/json';
      return config;
    } catch (err) {
      logger.error(
        err.config || err,
        'INTERCEPTOR[REQUEST]: HEADER_GENERATION_ERROR'
      );
      return Promise.reject('Unable to add callback header.');
    }
  }
}

module.exports = channelCallbackHttpClient.getInstance().client;
