const httpClient = require('./channelCallbackHttpClient');
const requestMapper = require('./requestMapper');

const execute = async (
  params,
  callbackUrl,
  installment,
  maxRetries = 3,
  delayMs = 60000
) => {
  let attempt = 0;
  while (attempt < maxRetries) {
    try {
      const request = requestMapper(params, installment);
      const response = await httpClient.post(callbackUrl, request);
      const { status: callbackResponseStatus, data: callbackResponse } =
        response;
      if (
        callbackResponseStatus === 200 &&
        callbackResponse.notificationResponse === 'accepted'
      ) {
        return response;
      }
      attempt++;
      if (attempt < maxRetries) {
        await delay(delayMs);
      }
    } catch (error) {
      return error;
    }
  }
  return {
    data: {
      callbackResponse: `Failed to fetch after ${maxRetries} attempts`
    },
    status: {
      callbackResponseStatus: 500
    }
  };
};

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

module.exports = execute;
