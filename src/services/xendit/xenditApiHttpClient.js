const logger = require('@configs/logger');
const Http = require('../../configurations/http');
const {
  XENDIT_HOST,
  XENDIT_SECRET_KEY,
  SKIP_SSL_VERIFICATION
} = require('@configs/env');

const {
  xendit: { basicAuth }
} = require('@utils');

class xenditApiHttpClient extends Http {
  constructor() {
    super(XENDIT_HOST, {
      rejectUnauthorized: !SKIP_SSL_VERIFICATION // optional
    });
    this.authorizationToken = null;
    this.authorizationTokenExpiry = null; // optional
    this.client.interceptors.request.use(
      this.#setAuthorizationHeader.bind(this),
      { synchronous: true }
    );
  }

  static getInstance() {
    if (!xenditApiHttpClient.instance) {
      xenditApiHttpClient.instance = new xenditApiHttpClient();
    }
    return xenditApiHttpClient.instance;
  }

  async #setAuthorizationHeader(config) {
    try {
      this.authorizationToken = basicAuth(XENDIT_SECRET_KEY);
      config.headers['Authorization'] = `Basic ${this.authorizationToken}`;
      config.headers['Content-Type'] = 'application/json';
      return config;
    } catch (err) {
      logger.error(
        err.config || err,
        'INTERCEPTOR[REQUEST]: ACCESS_TOKEN_GENERATION_ERROR'
      );
      return Promise.reject('Unable to generate access token.');
    }
  }
}

module.exports = xenditApiHttpClient.getInstance().client;
