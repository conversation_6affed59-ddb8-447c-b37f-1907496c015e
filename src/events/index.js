const logger = require('@configs/logger');
const {
  kafka: { extractEventFromKafkaHeaders, normalizeKafkaHeaders },
  kafkaSchemaRegistry: { decodeWithOptionalKafkaSchemaRegistryValidation }
} = require('@utils');

const handler = require('@events/handler');

async function messageHandler(params) {
  const { value: message, topic, offset, partition, headers } = params;

  logger.info({ topic, offset, partition }, 'KAFKA MESSAGE METADATA');

  const normalizedKafkaHeaders = normalizeKafkaHeaders(headers);

  logger.info(normalizedKafkaHeaders, 'KAFKA MESSAGE HEADERS');

  const eventName = extractEventFromKafkaHeaders(
    normalizedKafkaHeaders,
    handler
  );

  const decodedKafkaMessage =
    await decodeWithOptionalKafkaSchemaRegistryValidation(message);

  logger.info(decodedKafkaMessage, `Executing event: ${eventName}.`);

  await handler[eventName](decodedKafkaMessage);
}

module.exports = messageHandler;
