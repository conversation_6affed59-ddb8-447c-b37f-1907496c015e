const { name } = require('@root/package.json');
const logger = require('@configs/logger');
const ApplicationException = require('@exception');
const { api } = require('@services/xendit');
const { execute: channelCallback } = require('@services/channel');
const transactionRepository = require('@repositories/dynamodb/transactionRepository');
const channelRepository = require('@repositories/dynamodb/channelRepository');
const eventLogsRepository = require('@repositories/dynamodb/eventLogsRepository');
const settlementRepository = require('@repositories/dynamodb/settlementRepository');
const { writeDDBTransaction } = require('@utils/database');
const {
  uuid: { generateUuidv7, guid }
} = require('@utils');

const {
  KAFKA_TOPIC_POSTING,
  KAFKA_PRODUCER_SCHEME,
  KAFKA_MESSAGE_EVENT_POSTING
} = require('@configs/env');
const { kafkaSchemaRegistry } = require('@utils');
const KafkaProducerService = require('@configs/kafkaProducer');
const billPaymentCoreProducer = new KafkaProducerService(KAFKA_TOPIC_POSTING);
module.exports = async (params) => {
  try {
    logger.info(params, 'MESSAGE_PARAMETERS');

    const eventId = generateUuidv7();
    let isInstallment = false;
    const maxRetries = 3;

    const {
      paymentId,
      event: { callbackStatus, credit_card_charge_id, transactionTimestamp }
    } = params;
    const transaction = await transactionRepository.getTransaction(paymentId);
    if (!transaction) {
      await eventLogsRepository.createFailedEvent({
        paymentId,
        eventId,
        channel: { id: transaction?.channelId },
        eventName: 'PAYMENT_CALLBACK',
        eventSource: name,
        error: 'TRANSACTION_NOT_FOUND'
      });

      throw new ApplicationException('TRANSACTION_NOT_FOUND', {
        paymentId
      });
    }

    const { callbackUrl } = await channelRepository.getChannelById(
      transaction.channelId
    );

    // /**
    //  * Xendit Get Charge API
    //  */

    logger.info(params, 'CREDIT_CARD_PARAMS');

    const { data: xenditResponse, status: xenditResponseStatus } =
      await api.getCharge(credit_card_charge_id);

    logger.info(xenditResponse, 'XENDIT_GET_CHARGE_RESPONSE');

    if (
      Object.prototype.hasOwnProperty.call(xenditResponse, 'installment') &&
      xenditResponse.installment != null
    ) {
      isInstallment = true;
    }

    if (xenditResponseStatus > 201) {
      await eventLogsRepository.createFailedEvent({
        paymentId,
        eventId,
        channel: { id: transaction?.channelId },
        eventName: 'PAYMENT_SESSION',
        eventSource: name,
        xenditResponse
      });

      throw new ApplicationException('XENDIT_GET_CHARGE_FAILED', {
        xenditResponse
      });
    }

    /**
     * Send channel callback
     */
    const requestParams = {
      paymentId,
      callbackStatus,
      description:
        xenditResponse.network_response?.card_network_descriptor ?? '',
      card_issuing_bank: xenditResponse.card_issuing_bank ?? '',
      installment: xenditResponse.installment ?? ''
    };

    logger.info(callbackUrl, 'CALLBACK_URL');

    const { data: callbackResponse, status: callbackResponseStatus } =
      await channelCallback(
        requestParams,
        callbackUrl,
        isInstallment,
        maxRetries
      );

    logger.info(callbackResponseStatus !== 200, 'callbackResponseStatus');
    logger.info(
      callbackResponse.notificationResponse !== 'accepted',
      'callbackResponse'
    );
    if (
      callbackResponseStatus !== 200 ||
      callbackResponse.notificationResponse !== 'accepted'
    ) {
      await eventLogsRepository.createFailedEvent({
        paymentId,
        eventId,
        channel: { id: transaction?.channelId },
        eventName: 'PAYMENT_CALLBACK',
        eventSource: name,
        error: { callbackResponse }
      });
    } else {
      /**
       * TODO: save event log to ddb
       */
      const updateTransactionParams = transactionRepository.buildUpdateParams(
        {
          paymentId: transaction.paymentId,
          createDateTime: transaction.createDateTime
        },
        {
          status: 'CARD_AUTHORISED',
          merchant_id: xenditResponse.mid_label,
          paymentMethod: 'card',
          installment: xenditResponse.installment ?? '',
          card_issuing_bank: xenditResponse.card_issuing_bank ?? '',
          card_brand: xenditResponse.card_brand ?? ''
        }
      );

      const createEventParams = eventLogsRepository.buildCreateParams({
        paymentId,
        eventId,
        channelId: transaction.channelId,
        eventName: 'PAYMENT_CALLBACK',
        eventSource: name,
        eventStatus: 'SUCCESS_PAYMENT_CALLBACK',
        eventDetails: {
          params
        }
      });

      const settlementParams = transaction.settlementBreakdown.map(
        (settlement) => {
          const params = settlementRepository.buildCreateParams({
            paymentId: transaction.paymentId,
            transactionId: generateUuidv7(),
            createDateTime: transaction.createDateTime,
            accountId: settlement.accountId,
            accountType: settlement.accountType,
            mobileNumber: settlement.mobileNumber,
            email: settlement.emailAddress,
            transactionType: settlement.transactionType,
            amount: settlement.amountValue,
            brand: settlement.brand,
            status: 'CARD_AUTHORISED',
            postPaymentReferenceId: guid()
          });
          return { params, operation: 'Put' };
        }
      );

      await writeDDBTransaction([
        { params: updateTransactionParams, operation: 'Update' },
        { params: createEventParams, operation: 'Put' },
        ...settlementParams
      ]);
      logger.info(
        {
          paymentId,
          callbackResponse
        },
        `[${paymentId}]: PAYMENT_CALLBACK_RESULT`
      );
    }
    /**
     * Bill Payment Posting
     */
    const encodedMessage =
      await kafkaSchemaRegistry.encodeWithOptionalKafkaSchemaRegistryValidation(
        {
          eventType: KAFKA_MESSAGE_EVENT_POSTING,
          paymentId,
          accountDetails: {
            accountNumber: transaction.accountId,
            paidAt: transactionTimestamp
          }
        },
        KAFKA_PRODUCER_SCHEME
      );

    await billPaymentCoreProducer.produceMessage(
      encodedMessage,
      KAFKA_MESSAGE_EVENT_POSTING
    );
  } catch (error) {
    logger.error(error, 'PAYMENT_CALLBACK_ERROR');
  }
};
