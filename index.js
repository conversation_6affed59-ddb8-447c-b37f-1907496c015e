require('module-alias/register');
const { KAFKA_TOPICS } = require('@configs/env');
const { kafkaErrorHandler } = require('@configs/error');
const { KafkaConsumerService } = require('@configs/kafka');

const messageHandler = require('@events/index');

/**
 * Create an instance of KafkaConsumerService by providing a comma-separated list of Kafka topics to subscribe to:
 */
const kafkaConsumer = new KafkaConsumerService(KAFKA_TOPICS);

/**
 * Define function to handle Kafka messages by processing each message according to the application's requirements and performing any necessary operations.
 */
kafkaConsumer.setMessageHandler(messageHandler);

/**
 * Define a function that processes errors retrieved from Kafka topics.
 *
 * Note: This is only for kafka-related-errors, business errors should handled separately.
 */
kafkaConsumer.setErrorHandler(kafkaErrorHandler);
